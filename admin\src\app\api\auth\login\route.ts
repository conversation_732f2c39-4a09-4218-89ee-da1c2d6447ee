import { NextResponse, NextRequest } from "next/server";
import bcrypt from 'bcrypt';
import pool from '@/lib/db';
import { generateToken } from '@/lib/jwt-utils';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    let user = null;

    // First, try to authenticate with mock admin users
    try {
      const { mockAdminUsers, mockLoginCredentials } = await import('../../../../../main/src/data/mockUsers');

      const mockAdmin = mockAdminUsers.find(admin => admin.email === email.toLowerCase());
      if (mockAdmin) {
        const mockCredentials = mockLoginCredentials[email.toLowerCase()];
        if (mockCredentials && mockCredentials.password === password) {
          user = mockAdmin;
        } else {
          return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
        }
      }
    } catch (importError) {
      console.log('Mock data not available, falling back to database');
    }

    // If no mock user found, try database
    if (!user) {
      try {
        const result = await pool.query(
          'SELECT * FROM users WHERE email = $1 AND role = $2',
          [email.toLowerCase(), 'admin']
        );

        if (result.rows.length === 0) {
          return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
        }

        const dbUser = result.rows[0];

        // Verify password
        const passwordMatch = await bcrypt.compare(password, dbUser.password);
        if (!passwordMatch) {
          return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
        }

        user = dbUser;
      } catch (dbError) {
        console.log('Database not available, using mock data only');
        return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
      }
    }

    // Generate admin token
    const adminToken = generateToken({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    }, '4h'); // Longer session for admins

    // Create response
    const response = NextResponse.json({
      message: "Admin login successful",
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });

    // Set admin token cookie
    response.cookies.set("admin_token", adminToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'lax',
      path: '/',
      maxAge: 4 * 60 * 60 // 4 hours in seconds
    });

    return response;
  } catch (error) {
    console.error("Admin login error:", error);
    return NextResponse.json({ error: "Login failed" }, { status: 500 });
  }
}